//
//  ScratchCardViewModel.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI
import CoreData
import Combine

/**
 * 刮刮卡视图模型
 * 管理刮刮卡配置加载、状态管理、刮除逻辑和动画控制
 */
@MainActor
class ScratchCardViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var cardItems: [ScratchCardItem] = []
    @Published var isLoading = false
    @Published var showResult = false
    @Published var resultPrize = ""
    @Published var showInsufficientPoints = false
    @Published var showNoConfig = false
    @Published var particles: [ParticleItem] = []
    
    // MARK: - Scratch Card Specific States
    @Published var selectedCardIndex: Int? = nil
    @Published var showScratchOverlay = false
    @Published var scratchProgress: Double = 0.0
    @Published var prizeRevealed = false
    
    // MARK: - Private Properties
    private let member: Member
    private var scratchCardConfig: LotteryConfig?
    private var cancellables = Set<AnyCancellable>()
    private var particleTimer: Timer?
    
    // MARK: - Animation States
    @Published var animationStates: [UUID: ScratchCardAnimationState] = [:]
    
    // MARK: - Constants
    private let maxParticles = 30
    private let particleLifespan: Double = 1.2
    private let scratchThreshold: Double = 0.4
    
    // MARK: - Computed Properties
    
    /**
     * 获取每次刮除消耗的积分
     */
    var costPerScratch: Int {
        return Int(scratchCardConfig?.costPerPlay ?? 10)
    }
    
    /**
     * 检查成员是否有足够积分刮除
     */
    var canAffordScratching: Bool {
        return member.currentPoints >= costPerScratch
    }
    
    /**
     * 获取未刮除的卡片数量
     */
    var unscatchedCount: Int {
        return cardItems.filter { !$0.isScratched }.count
    }
    
    /**
     * 检查是否所有卡片都已刮除
     */
    var allCardsScratched: Bool {
        return cardItems.allSatisfy { $0.isScratched }
    }
    
    // MARK: - Initialization
    
    init(member: Member) {
        self.member = member
    }
    
    // MARK: - Public Methods
    
    /**
     * 加载刮刮卡配置
     */
    func loadScratchCardConfig(dataManager: DataManager) {
        isLoading = true
        
        // 获取成员的刮刮卡配置
        if let config = dataManager.getScratchCardConfig(for: member) {
            scratchCardConfig = config
            generateCardItems(from: config)
            showNoConfig = false
        } else {
            scratchCardConfig = nil
            cardItems = []
            showNoConfig = true
        }
        
        isLoading = false
    }
    
    /**
     * 选择指定索引的卡片
     */
    func selectCard(at index: Int, dataManager: DataManager) -> Bool {
        guard index < cardItems.count else { return false }
        guard cardItems[index].isClickable else { return false }
        guard canAffordScratching else {
            showInsufficientPoints = true
            return false
        }
        
        // 立即扣除积分（防止用户退回）
        deductMemberPoints(costPerScratch, dataManager: dataManager)
        
        // 设置选中状态
        selectedCardIndex = index
        cardItems[index].startScratching()
        
        // 开始选中动画
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            showScratchOverlay = true
        }
        
        return true
    }
    
    /**
     * 更新刮除进度
     */
    func updateScratchProgress(index: Int, progress: Double) {
        guard index < cardItems.count else { return }
        
        cardItems[index].updateScratchProgress(progress)
        scratchProgress = progress
        
        // 检查是否达到显示奖品的阈值
        if progress >= scratchThreshold && !prizeRevealed {
            revealPrize(at: index)
        }
    }
    
    /**
     * 显示奖品
     * 确保刮开时看到的奖品与最终获得的奖品完全一致
     */
    func revealPrize(at index: Int) {
        guard index < cardItems.count else { return }
        
        prizeRevealed = true
        cardItems[index].animationState = .revealing
        
        // 使用被刮开卡片的奖品，确保显示与获得的奖品一致
        let prize = cardItems[index].prizeName
        resultPrize = prize
        
        // 生成庆祝粒子
        generateCelebrationParticles()
        
        // 延迟显示结果弹窗
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                self.showResult = true
            }
        }
    }
    
    /**
     * 确认刮除结果
     */
    func confirmResult(dataManager: DataManager) {
        guard let selectedIndex = selectedCardIndex else { return }
        
        // 生成抽奖记录
        createLotteryRecord(prizeName: resultPrize, cost: costPerScratch, dataManager: dataManager)
        
        // 完成刮除
        cardItems[selectedIndex].completeScratching()
        
        // 重置状态
        resetScratchState()
    }
    
    /**
     * 重置所有卡片状态
     */
    func resetAllCards() {
        for i in 0..<cardItems.count {
            cardItems[i].reset()
        }
        animationStates.removeAll()
        particles.removeAll()
        stopParticleAnimation()
        resetScratchState()
    }
    
    // MARK: - Private Methods
    
    /**
     * 根据配置生成卡片项目
     * 实现真正的随机分配，确保每次进入页面奖品排列都不同
     */
    private func generateCardItems(from config: LotteryConfig) {
        let items = config.allItems
        // 随机打乱奖品列表，保证每种奖品都有对应卡片，但位置随机
        let shuffledItems = items.shuffled()
        
        cardItems = shuffledItems.enumerated().map { index, item in
            return ScratchCardItem.create(
                index: index,
                prizeName: item.prizeName ?? "神秘奖品",
                skin: .silver // 默认使用银色皮肤
            )
        }
    }
    
    /**
     * 生成庆祝粒子
     */
    private func generateCelebrationParticles() {
        let particleCount = 20
        let screenCenter = CGPoint(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 2)
        
        for _ in 0..<particleCount {
            let particle = ParticleItem.createCelebrationParticle(at: screenCenter)
            particles.append(particle)
        }
        
        // 启动粒子动画
        startParticleAnimation()
    }
    
    /**
     * 启动粒子动画
     */
    private func startParticleAnimation() {
        particleTimer?.invalidate()
        particleTimer = Timer.scheduledTimer(withTimeInterval: 1/60.0, repeats: true) { _ in
            Task { @MainActor in
                self.updateParticles()
            }
        }
    }
    
    /**
     * 停止粒子动画
     */
    private func stopParticleAnimation() {
        particleTimer?.invalidate()
        particleTimer = nil
    }
    
    /**
     * 更新粒子状态
     */
    private func updateParticles() {
        let deltaTime = 1.0 / 60.0
        
        for i in particles.indices {
            particles[i].update(deltaTime: deltaTime)
        }
        
        // 移除死亡的粒子
        particles.removeAll { !$0.isAlive }
        
        // 如果没有粒子了，停止动画
        if particles.isEmpty {
            stopParticleAnimation()
        }
    }
    
    /**
     * 创建抽奖记录
     */
    private func createLotteryRecord(prizeName: String, cost: Int, dataManager: DataManager) {
        let context = dataManager.persistenceController.container.viewContext

        let record = LotteryRecord(context: context)
        record.id = UUID()
        record.toolType = "scratchcard"
        record.prizeResult = prizeName
        record.cost = Int32(cost)
        record.timestamp = Date()
        record.member = member

        // 保存到CoreData
        dataManager.save()
    }
    
    /**
     * 扣除成员积分
     */
    private func deductMemberPoints(_ points: Int, dataManager: DataManager) {
        // 扣除积分
        member.currentPoints -= Int32(points)
        member.updatedAt = Date()
        
        // 保存更改
        dataManager.save()
    }
    
    /**
     * 重置刮除状态
     */
    private func resetScratchState() {
        selectedCardIndex = nil
        showScratchOverlay = false
        showResult = false
        resultPrize = ""
        scratchProgress = 0.0
        prizeRevealed = false
    }
}

// MARK: - Extensions

extension ScratchCardViewModel {
    
    /**
     * 获取随机奖品
     */
    private func getRandomPrize() -> String {
        guard let config = scratchCardConfig else { return "神秘奖品" }
        let items = config.allItems
        guard !items.isEmpty else { return "神秘奖品" }
        
        let randomIndex = Int.random(in: 0..<items.count)
        return items[randomIndex].prizeName ?? "神秘奖品"
    }
}

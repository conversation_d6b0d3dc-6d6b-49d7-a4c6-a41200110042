//
//  ScratchCardView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡主视图
 * 集成所有刮刮卡相关组件，提供完整的刮刮卡体验
 */
struct ScratchCardView: View {
    
    // MARK: - Properties
    let member: Member
    let onClose: () -> Void
    
    // MARK: - View Models
    @StateObject private var viewModel: ScratchCardViewModel
    @EnvironmentObject private var dataManager: DataManager
    
    // MARK: - State
    @State private var animationTrigger = false
    
    // MARK: - Initialization
    
    init(member: Member, onClose: @escaping () -> Void) {
        self.member = member
        self.onClose = onClose
        self._viewModel = StateObject(wrappedValue: ScratchCardViewModel(member: member))
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                backgroundGradient
                
                // 主要内容
                mainContent(geometry: geometry)
                
                // 刮除覆盖层
                if viewModel.showScratchOverlay, let selectedIndex = viewModel.selectedCardIndex {
                    scratchOverlay(cardIndex: selectedIndex)
                        .allowsHitTesting(true)
                }
                
                // 结果弹窗
                if viewModel.showResult {
                    resultOverlay
                }
                
                // 积分不足提示
                if viewModel.showInsufficientPoints {
                    insufficientPointsAlert
                }
                
                // 粒子效果层
                particleEffectLayer
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationTitle("scratch_card.title".localized)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                backButton
            }
        }
        .onAppear {
            viewModel.loadScratchCardConfig(dataManager: dataManager)
            withAnimation(.easeInOut(duration: 0.8)) {
                animationTrigger = true
            }
        }
    }
    
    // MARK: - Background Gradient
    
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                Color(hex: "#ffeef0").opacity(0.3),
                Color(hex: "#fff8e1").opacity(0.2),
                Color.white
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Main Content
    
    private func mainContent(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            // 顶部统计信息
            if !viewModel.cardItems.isEmpty {
                scratchCardStatsView
                    .padding(.top, 20)
                    .padding(.bottom, 16)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .offset(y: animationTrigger ? 0 : -30)
                    .animation(.easeInOut(duration: 0.8).delay(0.2), value: animationTrigger)
            }
            
            // 刮刮卡网格或空状态
            contentArea(geometry: geometry)
        }
    }
    
    // MARK: - Content Area
    
    @ViewBuilder
    private func contentArea(geometry: GeometryProxy) -> some View {
        if viewModel.isLoading {
            loadingView
        } else if viewModel.cardItems.isEmpty {
            emptyStateView
        } else {
            scratchCardGridView(geometry: geometry)
        }
    }
    
    // MARK: - Stats View
    
    private var scratchCardStatsView: some View {
        VStack(spacing: 12) {
            HStack {
                // 剩余卡片数
                HStack(spacing: 8) {
                    Image(systemName: "square.stack.3d.up")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(Color(hex: "#ff6b6b"))
                    
                    Text(String(format: "scratch_card.remaining_format".localized, viewModel.unscatchedCount))
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
                
                Spacer()
                
                // 消耗积分
                HStack(spacing: 8) {
                    Image(systemName: "star.circle.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(Color(hex: "#a9d051"))
                    
                    Text(String(format: "scratch_card.cost_format".localized, viewModel.costPerScratch))
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
            }
            
            // 当前积分
            HStack(spacing: 8) {
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.blue)
                
                Text(String(format: "scratch_card.current_points_format".localized, member.currentPoints))
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                // 积分状态
                Text(viewModel.canAffordScratching ? "scratch_card.points_sufficient".localized : "scratch_card.points_insufficient".localized)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(viewModel.canAffordScratching ? Color.green : Color.red)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill((viewModel.canAffordScratching ? Color.green : Color.red).opacity(0.1))
                    )
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.8))
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
        .padding(.horizontal, 20)
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .progressViewStyle(CircularProgressViewStyle(tint: Color(hex: "#ff6b6b")))
            
            Text("scratch_card.loading".localized)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white.opacity(0.8))
    }
    
    // MARK: - Empty State View
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "rectangle.stack")
                .font(.system(size: 64, weight: .light))
                .foregroundColor(DesignSystem.Colors.textTertiary)
            
            VStack(spacing: 8) {
                Text("scratch_card.empty.title".localized)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text("scratch_card.empty.description".localized)
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white.opacity(0.8))
    }
    
    // MARK: - Scratch Card Grid View
    
    private func scratchCardGridView(geometry: GeometryProxy) -> some View {
        ScratchCardGridView(
            cardItems: viewModel.cardItems,
            onCardTapped: { index in
                handleCardTapped(at: index)
            }
        )
        .opacity(animationTrigger ? 1.0 : 0.0)
        .offset(y: animationTrigger ? 0 : 50)
        .animation(.easeInOut(duration: 0.8).delay(0.4), value: animationTrigger)
    }
    
    // MARK: - Scratch Overlay
    
    private func scratchOverlay(cardIndex: Int) -> some View {
        ZStack {
            // 背景遮罩（不允许点击外面退回）
            Color.black.opacity(0.8)
                .ignoresSafeArea()
                .onTapGesture {
                    // 不执行任何操作，禁用点击外面退回
                }
            
            VStack(spacing: 20) {
                // 添加"开心刮刮卡"标题
                VStack(spacing: 20) {
                    Text("scratch_card.happy_title".localized)
                        .font(.system(size: 30, weight: .bold))
                        .foregroundColor(.white)
                        .shadow(color: Color.black.opacity(0.3), radius: 2, x: 0, y: 1)

                    Text("scratch_card.happy_subtitle".localized)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
                .padding(.top, 80)
                
                // 刮刮卡
                ScratchCardCanvasView(
                    cardItem: viewModel.cardItems[cardIndex],
                    onProgressUpdate: { progress in
                        viewModel.updateScratchProgress(index: cardIndex, progress: progress)
                    },
                    onScratchComplete: {
                        viewModel.revealPrize(at: cardIndex)
                    }
                )
                .scaleEffect(viewModel.cardItems[cardIndex].animationState == .selected ? 1.0 : 0.8)
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: viewModel.cardItems[cardIndex].animationState)
                
                Spacer()
            }
        }
    }
    
    // MARK: - Result Overlay
    
    private var resultOverlay: some View {
        ScratchCardResultView(
            prizeName: viewModel.resultPrize,
            costPoints: viewModel.costPerScratch,
            onConfirm: {
                viewModel.confirmResult(dataManager: dataManager)
            }
        )
        .zIndex(1001)
    }
    
    // MARK: - Alert Views
    
    private var insufficientPointsAlert: some View {
        VStack(spacing: 16) {
            Text("scratch_card.insufficient_points_title".localized)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Text(String(format: "scratch_card.insufficient_points_message_format".localized, viewModel.costPerScratch, member.currentPoints))
                .font(.system(size: 16))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)

            Button(action: {
                viewModel.showInsufficientPoints = false
            }) {
                Text("common.button.confirm".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color(hex: "#a9d051"))
                    .cornerRadius(8)
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
        .padding(.horizontal, 40)
        .zIndex(999)
    }
    
    // MARK: - Particle Effect Layer
    
    private var particleEffectLayer: some View {
        ParticleSystemView(
            particles: viewModel.particles,
            isActive: !viewModel.particles.isEmpty
        )
        .zIndex(998)
    }
    
    // MARK: - Back Button
    
    private var backButton: some View {
        Button(action: onClose) {
            HStack(spacing: 4) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .semibold))
                
                Text("scratch_card.back_button".localized)
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(DesignSystem.Colors.textPrimary)
        }
    }
    
    // MARK: - Event Handlers
    
    /**
     * 处理卡片点击事件
     */
    private func handleCardTapped(at index: Int) {
        let success = viewModel.selectCard(at: index, dataManager: dataManager)
        if !success {
            // 处理选择失败的情况（如积分不足）
            print("选择刮刮卡失败")
        }
    }
}

#Preview {
    NavigationView {
        ScratchCardView(
            member: Member(), // 需要提供一个示例Member对象
            onClose: {
                print("关闭刮刮卡视图")
            }
        )
    }
    .environmentObject(DataManager.shared)
}

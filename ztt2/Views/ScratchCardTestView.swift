//
//  ScratchCardTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡功能测试视图
 * 用于验证刮刮卡功能是否正常工作
 */
struct ScratchCardTestView: View {
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var testMember: Member?
    @State private var showScratchCard = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("刮刮卡功能测试")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()
                
                if let member = testMember {
                    VStack(spacing: 16) {
                        Text("测试成员: \(member.name ?? "未知")")
                            .font(.title2)
                        
                        Text("当前积分: \(member.currentPoints)")
                            .font(.title3)
                            .foregroundColor(.blue)
                        
                        Button(action: {
                            showScratchCard = true
                        }) {
                            Text("打开刮刮卡")
                                .font(.title2)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(
                                    LinearGradient(
                                        colors: [Color(hex: "#ff6b6b"), Color(hex: "#ee5a52")],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .cornerRadius(12)
                        }
                        .padding(.horizontal)
                        
                        Button(action: {
                            addTestPoints()
                        }) {
                            Text("添加测试积分 (+50)")
                                .font(.title3)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.green)
                                .cornerRadius(12)
                        }
                        .padding(.horizontal)
                        
                        Button(action: {
                            createTestConfig()
                        }) {
                            Text("创建测试配置")
                                .font(.title3)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.orange)
                                .cornerRadius(12)
                        }
                        .padding(.horizontal)
                    }
                } else {
                    VStack(spacing: 16) {
                        Text("正在创建测试成员...")
                            .font(.title2)
                            .foregroundColor(.gray)
                        
                        ProgressView()
                            .scaleEffect(1.5)
                    }
                }
                
                Spacer()
            }
            .navigationTitle("刮刮卡测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        .fullScreenCover(isPresented: $showScratchCard) {
            if let member = testMember {
                NavigationView {
                    ScratchCardView(
                        member: member,
                        onClose: {
                            showScratchCard = false
                        }
                    )
                }
                .environmentObject(dataManager)
            }
        }
        .onAppear {
            setupTestMember()
        }
    }
    
    // MARK: - Helper Methods
    
    /**
     * 设置测试成员
     */
    private func setupTestMember() {
        // 查找或创建测试成员
        if let existingMember = dataManager.members.first(where: { $0.name == "刮刮卡测试成员" }) {
            testMember = existingMember
        } else {
            // 创建新的测试成员
            if let newMember = dataManager.createMember(
                name: "刮刮卡测试成员",
                role: "测试",
                birthDate: Date(),
                initialPoints: 100
            ) {
                testMember = newMember
            }
        }
    }
    
    /**
     * 添加测试积分
     */
    private func addTestPoints() {
        guard let member = testMember else { return }
        
        member.currentPoints += 50
        member.updatedAt = Date()
        dataManager.save()
    }
    
    /**
     * 创建测试配置
     */
    private func createTestConfig() {
        guard let member = testMember else { return }
        
        // 创建刮刮卡配置
        let prizes = [
            "小贴纸",
            "铅笔",
            "橡皮",
            "尺子",
            "小玩具",
            "糖果",
            "书签",
            "彩色笔",
            "笔记本"
        ]
        
        let success = dataManager.saveScratchCardConfig(
            for: member,
            itemCount: prizes.count,
            costPerPlay: 10,
            prizeNames: prizes
        )
        
        if success {
            print("✅ 刮刮卡测试配置创建成功")
        } else {
            print("❌ 刮刮卡测试配置创建失败")
        }
    }
}

#Preview {
    ScratchCardTestView()
        .environmentObject(DataManager.shared)
}

//
//  FloatingAnimationModifier.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 浮动动画修饰器
 * 为视图添加轻微的浮动效果，增强视觉吸引力
 */
struct FloatingAnimationModifier: ViewModifier {
    
    let offset: CGFloat
    let rotation: Double
    let delay: Double
    let duration: Double
    let enabled: Bool
    
    @State private var isAnimating = false
    
    func body(content: Content) -> some View {
        content
            .offset(y: enabled && isAnimating ? offset : 0)
            .rotationEffect(.degrees(enabled && isAnimating ? rotation : 0))
            .animation(
                enabled ? 
                    .easeInOut(duration: duration)
                    .repeatForever(autoreverses: true)
                    .delay(delay) : 
                    .default,
                value: isAnimating
            )
            .onAppear {
                if enabled {
                    withAnimation {
                        isAnimating = true
                    }
                }
            }
            .onChange(of: enabled) { newValue in
                if newValue {
                    withAnimation {
                        isAnimating = true
                    }
                } else {
                    withAnimation(.easeOut(duration: 0.3)) {
                        isAnimating = false
                    }
                }
            }
    }
}

extension View {
    /**
     * 添加浮动动画效果
     */
    func floating(
        offset: CGFloat = 5,
        rotation: Double = 2,
        delay: Double = 0,
        duration: Double = 2.0,
        enabled: Bool = true
    ) -> some View {
        self.modifier(
            FloatingAnimationModifier(
                offset: offset,
                rotation: rotation,
                delay: delay,
                duration: duration,
                enabled: enabled
            )
        )
    }
}

/**
 * 粒子系统视图
 * 用于显示庆祝和刮除粒子效果
 */
struct ParticleSystemView: View {
    
    let particles: [ParticleItem]
    let isActive: Bool
    
    var body: some View {
        ZStack {
            ForEach(particles) { particle in
                Circle()
                    .fill(particle.color)
                    .frame(width: particle.size, height: particle.size)
                    .position(particle.currentPosition)
                    .opacity(particle.opacity)
                    .scaleEffect(particle.isAlive ? 1.0 : 0.0)
                    .animation(.easeOut(duration: 0.3), value: particle.isAlive)
            }
        }
        .allowsHitTesting(false)
    }
}

/**
 * 刮刮卡结果弹窗视图
 */
struct ScratchCardResultView: View {
    
    let prizeName: String
    let costPoints: Int
    let onConfirm: () -> Void
    
    @State private var animationTrigger = false
    @State private var celebrationTriggered = false
    @State private var glowIntensity: Double = 0.0
    @State private var sparkleAnimation = false
    
    var body: some View {
        ZStack {
            // 背景遮罩
            Color.black.opacity(0.6)
                .ignoresSafeArea()
                .onTapGesture {
                    // 防止点击背景关闭
                }
            
            // 结果卡片
            VStack(spacing: 24) {
                // 庆祝图标
                ZStack {
                    // 光晕效果
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    Color.yellow.opacity(glowIntensity),
                                    Color.orange.opacity(glowIntensity * 0.5),
                                    Color.clear
                                ],
                                center: .center,
                                startRadius: 0,
                                endRadius: 60
                            )
                        )
                        .frame(width: 120, height: 120)
                    
                    // 主图标
                    Image(systemName: "gift.fill")
                        .font(.system(size: 48, weight: .medium))
                        .foregroundColor(.yellow)
                        .scaleEffect(animationTrigger ? 1.0 : 0.3)
                        .rotationEffect(.degrees(sparkleAnimation ? 360 : 0))
                }
                
                // 恭喜文本
                VStack(spacing: 12) {
                    Text("scratch_card.result.congratulations".localized)
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .opacity(animationTrigger ? 1.0 : 0.0)

                    Text(prizeName)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(Color(hex: "#a9d051"))
                        .multilineTextAlignment(.center)
                        .opacity(animationTrigger ? 1.0 : 0.0)

                    Text(String(format: "scratch_card.result.cost_format".localized, costPoints))
                        .font(.system(size: 16))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .opacity(animationTrigger ? 1.0 : 0.0)
                }
                
                // 确认按钮
                Button(action: onConfirm) {
                    Text("scratch_card.result.confirm".localized)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            LinearGradient(
                                colors: [Color(hex: "#a9d051"), Color(hex: "#8bc34a")],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .cornerRadius(12)
                }
                .opacity(animationTrigger ? 1.0 : 0.0)
                .scaleEffect(animationTrigger ? 1.0 : 0.8)
            }
            .padding(32)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.2), radius: 20, x: 0, y: 10)
            )
            .padding(.horizontal, 40)
            .scaleEffect(animationTrigger ? 1.0 : 0.7)
            .opacity(animationTrigger ? 1.0 : 0.0)
        }
        .onAppear {
            startAnimationSequence()
        }
    }
    
    /**
     * 启动动画序列
     */
    private func startAnimationSequence() {
        // 阶段1：弹窗出现
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            animationTrigger = true
        }
        
        // 阶段2：启动庆祝效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            celebrationTriggered = true
            
            // 光晕动画
            withAnimation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
                glowIntensity = 0.6
            }
            
            // 闪烁动画
            withAnimation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true)) {
                sparkleAnimation = true
            }
        }
    }
}

#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        ScratchCardResultView(
            prizeName: "小贴纸",
            costPoints: 10,
            onConfirm: {
                print("确认获得奖品!")
            }
        )
    }
}

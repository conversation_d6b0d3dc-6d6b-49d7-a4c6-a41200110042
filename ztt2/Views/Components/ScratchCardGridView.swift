//
//  ScratchCardGridView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡网格视图组件
 * 支持多张刮刮卡的展示、选择和状态管理
 */
struct ScratchCardGridView: View {
    
    // MARK: - Properties
    let cardItems: [ScratchCardItem]
    let onCardTapped: (Int) -> Void
    
    // MARK: - State
    @State private var animationTrigger = false
    
    // MARK: - Constants
    private let columns = 3
    private let spacing: CGFloat = 16
    private let cardAspectRatio: CGFloat = 1.2
    
    var body: some View {
        GeometryReader { geometry in
            let availableWidth = geometry.size.width - (spacing * CGFloat(columns + 1))
            let itemWidth = availableWidth / CGFloat(columns)
            let itemHeight = itemWidth * cardAspectRatio
            let itemSize = CGSize(width: itemWidth, height: itemHeight)
            
            ScrollView {
                LazyVGrid(
                    columns: Array(repeating: GridItem(.flexible(), spacing: spacing), count: columns),
                    spacing: spacing
                ) {
                    ForEach(Array(cardItems.enumerated()), id: \.element.id) { index, cardItem in
                        scratchCardItemView(
                            cardItem: cardItem,
                            index: index,
                            size: itemSize
                        )
                        .opacity(animationTrigger ? 1.0 : 0.0)
                        .offset(y: animationTrigger ? 0 : 30)
                        .animation(
                            .spring(response: 0.6, dampingFraction: 0.8)
                                .delay(Double(index) * 0.1),
                            value: animationTrigger
                        )
                    }
                }
                .padding(.horizontal, spacing)
                .padding(.vertical, 20)
            }
        }
        .onAppear {
            withAnimation {
                animationTrigger = true
            }
        }
    }
    
    // MARK: - Scratch Card Item View
    
    /**
     * 单张刮刮卡项目视图
     */
    private func scratchCardItemView(cardItem: ScratchCardItem, index: Int, size: CGSize) -> some View {
        VStack(spacing: 8) {
            // 刮刮卡主体
            ScratchCardItemView(
                cardItem: cardItem,
                size: size,
                onTap: {
                    onCardTapped(index)
                }
            )
            .floating(
                offset: CGFloat.random(in: 4...8),
                rotation: Double.random(in: 1...4),
                delay: Double(index) * 0.2,
                duration: Double.random(in: 2.0...2.8),
                enabled: !cardItem.isScratched && cardItem.animationState == .idle
            )
            
            // 刮刮卡信息
            cardInfoView(cardItem: cardItem, index: index)
        }
        .contentShape(Rectangle()) // 扩大点击区域
    }
    
    // MARK: - Card Info View
    
    /**
     * 刮刮卡信息视图
     */
    private func cardInfoView(cardItem: ScratchCardItem, index: Int) -> some View {
        VStack(spacing: 4) {
            // 卡片标题
            Text(cardItem.displayTitle)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            // 状态标签
            statusLabel(for: cardItem)
        }
    }
    
    /**
     * 状态标签
     */
    private func statusLabel(for cardItem: ScratchCardItem) -> some View {
        HStack(spacing: 6) {
            Image(systemName: statusIcon(for: cardItem))
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(statusColor(for: cardItem))
            
            Text(statusText(for: cardItem))
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(statusColor(for: cardItem))
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(statusColor(for: cardItem).opacity(0.1))
        )
        .overlay(
            Capsule()
                .stroke(
                    LinearGradient(
                        colors: [
                            statusColor(for: cardItem).opacity(0.6),
                            statusColor(for: cardItem).opacity(0.4)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1.5
                )
        )
        .shadow(
            color: statusColor(for: cardItem).opacity(0.3),
            radius: 3,
            x: 0,
            y: 1
        )
    }
    
    // MARK: - Status Helpers
    
    /**
     * 获取状态图标
     */
    private func statusIcon(for cardItem: ScratchCardItem) -> String {
        switch cardItem.animationState {
        case .idle:
            return cardItem.isScratched ? "checkmark.circle.fill" : "hand.point.up.left"
        case .selected:
            return "hand.tap"
        case .scratching:
            return "hand.draw"
        case .revealing:
            return "sparkles"
        case .completed:
            return "trophy.fill"
        }
    }
    
    /**
     * 获取状态文本
     */
    private func statusText(for cardItem: ScratchCardItem) -> String {
        switch cardItem.animationState {
        case .idle:
            return cardItem.isScratched ? "scratch_card.status.completed".localized : "scratch_card.status.available".localized
        case .selected:
            return "scratch_card.status.selected".localized
        case .scratching:
            return "scratch_card.status.scratching".localized
        case .revealing:
            return "scratch_card.status.revealing".localized
        case .completed:
            return "scratch_card.status.completed".localized
        }
    }
    
    /**
     * 获取状态颜色
     */
    private func statusColor(for cardItem: ScratchCardItem) -> Color {
        switch cardItem.animationState {
        case .idle:
            return cardItem.isScratched ? Color.green : Color(hex: "#a9d051")
        case .selected:
            return Color.blue
        case .scratching:
            return Color.orange
        case .revealing:
            return Color.purple
        case .completed:
            return Color.green
        }
    }
}

/**
 * 单张刮刮卡项目视图
 */
struct ScratchCardItemView: View {
    
    let cardItem: ScratchCardItem
    let size: CGSize
    let onTap: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        ZStack {
            // 卡片背景
            cardBackground
            
            // 卡片内容
            cardContent
            
            // 刮除状态覆盖层
            if cardItem.isScratched {
                scratchedOverlay
            }
        }
        .frame(width: size.width, height: size.height)
        .scaleEffect(cardItem.scaleEffect * (isPressed ? 0.95 : 1.0))
        .opacity(cardItem.opacity)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        .animation(.easeInOut(duration: 0.6), value: cardItem.animationState)
        .onTapGesture {
            if cardItem.isClickable {
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
                
                onTap()
            }
        }
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing && cardItem.isClickable
        }, perform: {})
    }
    
    // MARK: - Card Background
    
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(
                LinearGradient(
                    colors: cardItem.isScratched ? 
                        [Color.green.opacity(0.1), Color.green.opacity(0.05)] :
                        [Color.white, Color.gray.opacity(0.05)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        cardItem.isScratched ? 
                            Color.green.opacity(0.3) : 
                            Color.gray.opacity(0.2),
                        lineWidth: 1.5
                    )
            )
            .shadow(
                color: Color.black.opacity(cardItem.isSelected ? 0.15 : 0.08),
                radius: cardItem.isSelected ? 8 : 4,
                x: 0,
                y: cardItem.isSelected ? 4 : 2
            )
    }
    
    // MARK: - Card Content
    
    private var cardContent: some View {
        VStack(spacing: 8) {
            // 刮刮卡图片
            Image(cardItem.isScratched ? "刮刮卡中奖" : "刮刮卡")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(height: size.height * 0.6)
                .cornerRadius(8)
            
            // 奖品预览（仅在已刮除时显示）
            if cardItem.isScratched {
                Text(cardItem.prizeName)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)
                    .padding(.horizontal, 4)
            }
        }
        .padding(8)
    }
    
    // MARK: - Scratched Overlay
    
    private var scratchedOverlay: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(Color.green.opacity(0.1))
            .overlay(
                VStack {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(.green)
                    
                    Text("scratch_card.status.completed".localized)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.green)
                }
            )
    }
}

#Preview {
    ScratchCardGridView(
        cardItems: [
            ScratchCardItem.create(index: 0, prizeName: "小贴纸"),
            ScratchCardItem.create(index: 1, prizeName: "铅笔"),
            ScratchCardItem.create(index: 2, prizeName: "橡皮"),
            ScratchCardItem.create(index: 3, prizeName: "尺子"),
            ScratchCardItem.create(index: 4, prizeName: "小玩具"),
            ScratchCardItem.create(index: 5, prizeName: "糖果")
        ],
        onCardTapped: { index in
            print("点击了刮刮卡 \(index)")
        }
    )
    .padding()
}
